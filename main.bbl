% Generated by IEEEtran.bst, version: 1.14 (2015/08/26)
\begin{thebibliography}{10}
\providecommand{\url}[1]{#1}
\csname url@samestyle\endcsname
\providecommand{\newblock}{\relax}
\providecommand{\bibinfo}[2]{#2}
\providecommand{\BIBentrySTDinterwordspacing}{\spaceskip=0pt\relax}
\providecommand{\BIBentryALTinterwordstretchfactor}{4}
\providecommand{\BIBentryALTinterwordspacing}{\spaceskip=\fontdimen2\font plus
\BIBentryALTinterwordstretchfactor\fontdimen3\font minus \fontdimen4\font\relax}
\providecommand{\BIBforeignlanguage}[2]{{%
\expandafter\ifx\csname l@#1\endcsname\relax
\typeout{** WARNING: IEEEtran.bst: No hyphenation pattern has been}%
\typeout{** loaded for the language `#1'. Using the pattern for}%
\typeout{** the default language instead.}%
\else
\language=\csname l@#1\endcsname
\fi
#2}}
\providecommand{\BIBdecl}{\relax}
\BIBdecl

\bibitem{zhu2017DLReview}
X.~X. Zhu, D.~Tuia, L.~Mou, G.-S. Xia, L.~Zhang, F.~Xu, and F.~Fraundorfer, ``Deep learning in remote sensing: A comprehensive review and list of resources,'' \emph{IEEE geoscience and remote sensing magazine}, vol.~5, no.~4, pp. 8--36, 2017.

\bibitem{Vargas2021OSM}
J.~E. Vargas-Munoz, S.~Srivastava, D.~Tuia, and A.~X. Falcão, ``Open{S}treet{M}ap: Challenges and opportunities in machine learning and remote sensing,'' \emph{IEEE Geoscience and Remote Sensing Magazine}, vol.~9, no.~1, pp. 184--199, 2021.

\bibitem{albrecht2021autogeolabel}
C.~M. Albrecht, F.~Marianno, and L.~J. Klein, ``Auto{G}eo{L}abel: Automated label generation for geospatial machine learning,'' in \emph{2021 IEEE International Conference on Big Data (Big Data)}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, 2021, pp. 1779--1786.

\bibitem{brown2022DynamicWorld}
C.~F. Brown, S.~P. Brumby, B.~Guzder-Williams, T.~Birch, S.~B. Hyde, J.~Mazzariello, W.~Czerwinski, V.~J. Pasquarella, R.~Haertel, S.~Ilyushchenko \emph{et~al.}, ``Dynamic world, near real-time global 10 m land use land cover mapping,'' \emph{Scientific Data}, vol.~9, no.~1, p. 251, 2022.

\bibitem{Zanaga2021WorldCover}
\BIBentryALTinterwordspacing
D.~Zanaga, R.~Van De~Kerchove, W.~De~Keersmaecker, N.~Souverijns, C.~Brockmann, R.~Quast, J.~Wevers, A.~Grosu, A.~Paccini, S.~Vergnaud, O.~Cartus, M.~Santoro, S.~Fritz, I.~Georgieva, M.~Lesiv, S.~Carter, M.~Herold, L.~Li, N.-E. Tsendbazar, F.~Ramoino, and O.~Arino, ``Esa worldcover 10 m 2020 v100,'' Oct. 2021. [Online]. Available: \url{https://doi.org/10.5281/zenodo.5571936}
\BIBentrySTDinterwordspacing

\bibitem{Karra2021ESRI}
K.~Karra, C.~Kontgis, Z.~Statman-Weil, J.~C. Mazzariello, M.~Mathis, and S.~P. Brumby, ``Global land use / land cover with sentinel 2 and deep learning,'' in \emph{2021 IEEE International Geoscience and Remote Sensing Symposium IGARSS}, 2021, pp. 4704--4707.

\bibitem{zhu2020so2sat}
X.~X. Zhu, J.~Hu, C.~Qiu, Y.~Shi, J.~Kang, L.~Mou, H.~Bagheri, M.~Haberle, Y.~Hua, R.~Huang \emph{et~al.}, ``So2sat lcz42: A benchmark data set for the classification of global local climate zones [software and data sets],'' \emph{IEEE Geoscience and Remote Sensing Magazine}, vol.~8, no.~3, pp. 76--89, 2020.

\bibitem{zhang2021understanding}
C.~Zhang, S.~Bengio, M.~Hardt, B.~Recht, and O.~Vinyals, ``Understanding deep learning (still) requires rethinking generalization,'' \emph{Communications of the ACM}, vol.~64, no.~3, pp. 107--115, 2021.

\bibitem{liu2022peas}
C.~Liu, C.~M. Albrecht, Y.~Wang, and X.~X. Zhu, ``Peaks fusion assisted early-stopping strategy for overhead imagery segmentation with noisy labels,'' in \emph{2022 IEEE International Conference on Big Data (Big Data)}, 2022, pp. 4842--4847.

\bibitem{hansch2019truth}
R.~H{\"a}nsch and O.~Hellwich, ``The truth about ground truth: Label noise in human-generated reference data,'' in \emph{IGARSS 2019-2019 IEEE International Geoscience and Remote Sensing Symposium}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, 2019, pp. 5594--5597.

\bibitem{wang2020consist}
G.~Wang, X.~Liu, C.~Li, Z.~Xu, J.~Ruan, H.~Zhu, T.~Meng, K.~Li, N.~Huang, and S.~Zhang, ``A noise-robust framework for automatic segmentation of covid-19 pneumonia lesions from ct images,'' \emph{IEEE Transactions on Medical Imaging}, vol.~39, no.~8, pp. 2653--2663, 2020.

\bibitem{li2022road}
P.~Li, X.~He, M.~Qiao, X.~Cheng, J.~Li, X.~Guo, T.~Zhou, D.~Song, M.~Chen, D.~Miao, Y.~Jiang, and Z.~Tian, ``Exploring label probability sequence to robustly learn deep convolutional neural networks for road extraction with noisy datasets,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~60, pp. 1--18, 2022.

\bibitem{he2018hsireview}
L.~He, J.~Li, C.~Liu, and S.~Li, ``Recent advances on spectral–spatial hyperspectral image classification: An overview and new guidelines,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~56, no.~3, pp. 1579--1597, 2018.

\bibitem{Maiti2022misalign}
A.~Maiti, S.~J. Oude~Elberink, and G.~Vosselman, ``Effect of label noise in semantic segmentation of high resolution aerial images and height data,'' \emph{ISPRS Annals of the Photogrammetry, Remote Sensing and Spatial Information Sciences}, vol. V-2-2022, pp. 275--282, 2022.

\bibitem{shen2023survey}
W.~Shen, Z.~Peng, X.~Wang, H.~Wang, J.~Cen, D.~Jiang, L.~Xie, X.~Yang, and Q.~Tian, ``A survey on label-efficient deep image segmentation: Bridging the gap between weak supervision and dense prediction,'' \emph{IEEE Transactions on Pattern Analysis and Machine Intelligence}, 2023.

\bibitem{zhang2020reliability}
B.~Zhang, J.~Xiao, Y.~Wei, M.~Sun, and K.~Huang, ``Reliability does matter: An end-to-end weakly supervised semantic segmentation approach,'' in \emph{Proceedings of the AAAI Conference on Artificial Intelligence}, vol.~34, no.~07, 2020, pp. 12\,765--12\,772.

\bibitem{zhang2023selftrain}
F.~Zhang, Y.~Shi, Z.~Xiong, W.~Huang, and X.~X. Zhu, ``Pseudo features-guided self-training for domain adaptive semantic segmentation of satellite images,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~61, pp. 1--14, 2023.

\bibitem{cao2022green}
Y.~Cao and X.~Huang, ``A coarse-to-fine weakly supervised learning method for green plastic cover segmentation using high-resolution remote sensing images,'' \emph{ISPRS Journal of Photogrammetry and Remote Sensing}, vol. 188, pp. 157--176, 2022.

\bibitem{dong2022landcover}
R.~Dong, W.~Fang, H.~Fu, L.~Gan, J.~Wang, and P.~Gong, ``High-resolution land cover mapping through learning with noise correction,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~60, pp. 1--13, 2022.

\bibitem{cao2023building}
Y.~Cao and X.~Huang, ``A full-level fused cross-task transfer learning method for building change detection using noise-robust pretrained networks on crowdsourced labels,'' \emph{Remote Sensing of Environment}, vol. 284, p. 113371, 2023.

\bibitem{liu2022adele}
S.~Liu, K.~Liu, W.~Zhu, Y.~Shen, and C.~Fernandez-Granda, ``Adaptive early-learning correction for segmentation from noisy annotations,'' in \emph{Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition}, 2022, pp. 2606--2616.

\bibitem{gawlikowski2023uncertianty}
J.~Gawlikowski, C.~R.~N. Tassi, M.~Ali, J.~Lee, M.~Humt, J.~Feng, A.~Kruspe, R.~Triebel, P.~Jung, R.~Roscher \emph{et~al.}, ``A survey of uncertainty in deep neural networks,'' \emph{Artificial Intelligence Review}, pp. 1--77, 2023.

\bibitem{brovelli2018osmbuildingassess}
M.~A. Brovelli and G.~Zamboni, ``A new method for the assessment of spatial accuracy and completeness of openstreetmap building footprints,'' \emph{ISPRS International Journal of Geo-Information}, vol.~7, no.~8, p. 289, 2018.

\bibitem{zhang2022osmbuildingassess}
Y.~Zhang, Q.~Zhou, M.~A. Brovelli, and W.~Li, ``Assessing osm building completeness using population data,'' \emph{International Journal of Geographical Information Science}, vol.~36, no.~7, pp. 1443--1466, 2022.

\bibitem{herfort2023buildingcomplete}
B.~Herfort, S.~Lautenbach, J.~Porto~de Albuquerque, J.~Anderson, and A.~Zipf, ``A spatio-temporal analysis investigating completeness and inequalities of global urban building data in openstreetmap,'' \emph{Nature Communications}, vol.~14, no.~1, p. 3985, 2023.

\bibitem{tarvainen2017meanteacher}
A.~Tarvainen and H.~Valpola, ``Mean teachers are better role models: Weight-averaged consistency targets improve semi-supervised deep learning results,'' in \emph{Advances in neural information processing systems}, vol.~30, 2017.

\bibitem{wei2022learning}
J.~Wei, Z.~Zhu, H.~Cheng, T.~Liu, G.~Niu, and Y.~Liu, ``Learning with noisy labels revisited: A study using real-world human annotations,'' in \emph{International Conference on Learning Representations}, 2022.

\bibitem{sukhbaatar2014training}
S.~Sukhbaatar, J.~Bruna, M.~Paluri, L.~Bourdev, and R.~Fergus, ``Training convolutional networks with noisy labels,'' \emph{arXiv preprint arXiv:1406.2080}, 2014.

\bibitem{bekker2016training}
A.~J. Bekker and J.~Goldberger, ``Training deep neural-networks based on unreliable labels,'' in \emph{2016 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, 2016, pp. 2682--2686.

\bibitem{goldberger2017training}
J.~Goldberger and E.~Ben-Reuven, ``Training deep neural-networks using a noise adaptation layer,'' in \emph{International conference on learning representations}, 2017.

\bibitem{tanaka2018joint}
D.~Tanaka, D.~Ikami, T.~Yamasaki, and K.~Aizawa, ``Joint optimization framework for learning with noisy labels,'' in \emph{Proceedings of the IEEE conference on computer vision and pattern recognition}, 2018, pp. 5552--5560.

\bibitem{pleiss2020identifying}
G.~Pleiss, T.~Zhang, E.~Elenberg, and K.~Q. Weinberger, ``Identifying mislabeled data using the area under the margin ranking,'' \emph{Advances in Neural Information Processing Systems}, vol.~33, pp. 17\,044--17\,056, 2020.

\bibitem{malach2017decoupling}
E.~Malach and S.~Shalev-Shwartz, ``Decoupling" when to update" from" how to update",'' \emph{Advances in neural information processing systems}, vol.~30, 2017.

\bibitem{jiang2018mentornet}
L.~Jiang, Z.~Zhou, T.~Leung, L.-J. Li, and L.~Fei-Fei, ``Mentornet: Learning data-driven curriculum for very deep neural networks on corrupted labels,'' in \emph{ICML}, 2018.

\bibitem{han2018coteaching}
B.~Han, Q.~Yao, X.~Yu, G.~Niu, M.~Xu, W.~Hu, I.~Tsang, and M.~Sugiyama, ``Co-teaching: Robust training of deep neural networks with extremely noisy labels,'' \emph{Advances in neural information processing systems}, vol.~31, 2018.

\bibitem{yu2019coteachingplus}
X.~Yu, B.~Han, J.~Yao, G.~Niu, I.~Tsang, and M.~Sugiyama, ``How does disagreement help generalization against label corruption?'' in \emph{International Conference on Machine Learning}.\hskip 1em plus 0.5em minus 0.4em\relax PMLR, 2019, pp. 7164--7173.

\bibitem{li2020dividemix}
J.~Li, R.~Socher, and S.~C. Hoi, ``Dividemix: Learning with noisy labels as semi-supervised learning,'' in \emph{International Conference on Learning Representations}, 2020.

\bibitem{zhang2022ideal}
P.-F. Zhang, Z.~Huang, G.~Bai, and X.-S. Xu, ``Ideal: High-order-ensemble adaptation network for learning with noisy labels,'' in \emph{Proceedings of the 30th ACM International Conference on Multimedia}, 2022, pp. 325--333.

\bibitem{liu2020elr}
S.~Liu, J.~Niles-Weed, N.~Razavian, and C.~Fernandez-Granda, ``Early-learning regularization prevents memorization of noisy labels,'' \emph{Advances in neural information processing systems}, vol.~33, pp. 20\,331--20\,342, 2020.

\bibitem{yi2019pencil}
K.~Yi and J.~Wu, ``Probabilistic end-to-end noise correction for learning with noisy labels,'' in \emph{Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition}, 2019, pp. 7017--7025.

\bibitem{Reed2015boot}
S.~E. Reed and H.~Lee, ``Training deep neural networks on noisy labels with bootstrapping,'' in \emph{International Conference on Learning Representations 2015 (ICLR 2015)}, 2015.

\bibitem{zhang2018generalized}
Z.~Zhang and M.~Sabuncu, ``Generalized cross entropy loss for training deep neural networks with noisy labels,'' \emph{Advances in neural information processing systems}, vol.~31, 2018.

\bibitem{lyu2019curriculum}
Y.~Lyu and I.~W. Tsang, ``Curriculum loss: Robust learning and generalization against label corruption,'' \emph{arXiv preprint arXiv:1905.10045}, 2019.

\bibitem{liu2020peer}
Y.~Liu and H.~Guo, ``Peer loss functions: Learning from noisy labels without knowing noise rates,'' in \emph{International conference on machine learning}.\hskip 1em plus 0.5em minus 0.4em\relax PMLR, 2020, pp. 6226--6236.

\bibitem{song2022learning}
H.~Song, M.~Kim, D.~Park, Y.~Shin, and J.-G. Lee, ``Learning from noisy labels with deep neural networks: A survey,'' \emph{IEEE Transactions on Neural Networks and Learning Systems}, 2022.

\bibitem{manwani2013noise}
N.~Manwani and P.~Sastry, ``Noise tolerance under risk minimization,'' \emph{IEEE transactions on cybernetics}, vol.~43, no.~3, pp. 1146--1151, 2013.

\bibitem{ghosh2017robust}
A.~Ghosh, H.~Kumar, and P.~S. Sastry, ``Robust loss functions under label noise for deep neural networks,'' in \emph{Proceedings of the AAAI conference on artificial intelligence}, vol.~31, no.~1, 2017.

\bibitem{tai2020coastal}
X.~Tai, G.~Wang, C.~Grecos, and P.~Ren, ``Coastal image classification under noisy labels,'' \emph{Journal of Coastal Research}, vol. 102, no.~SI, pp. 151--156, 2020.

\bibitem{huang2020smoothloss}
Z.~Huang, C.~O. Dumitru, Z.~Pan, B.~Lei, and M.~Datcu, ``Classification of large-scale high-resolution sar images with deep transfer learning,'' \emph{IEEE Geoscience and Remote Sensing Letters}, vol.~18, no.~1, pp. 107--111, 2020.

\bibitem{damodaran2020entropic}
B.~B. Damodaran, R.~Flamary, V.~Seguy, and N.~Courty, ``An entropic optimal transport loss for learning deep neural networks under label noise in remote sensing images,'' \emph{Computer Vision and Image Understanding}, vol. 191, p. 102863, 2020.

\bibitem{li2020error}
Y.~Li, Y.~Zhang, and Z.~Zhu, ``Error-tolerant deep learning for remote sensing image scene classification,'' \emph{IEEE transactions on cybernetics}, vol.~51, no.~4, pp. 1756--1768, 2020.

\bibitem{tu2020robust}
B.~Tu, W.~Kuang, W.~He, G.~Zhang, and Y.~Peng, ``Robust learning of mislabeled training samples for remote sensing image scene classification,'' \emph{IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing}, vol.~13, pp. 5623--5639, 2020.

\bibitem{kang2021neighbor}
J.~Kang, R.~Fernandez-Beltran, X.~Kang, J.~Ni, and A.~Plaza, ``Noise-tolerant deep neighborhood embedding for remotely sensed images with label noise,'' \emph{IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing}, vol.~14, pp. 2551--2562, 2021.

\bibitem{kang2020rnsl}
J.~Kang, R.~Fernandez-Beltran, P.~Duan, X.~Kang, and A.~J. Plaza, ``Robust normalized softmax loss for deep metric learning-based characterization of remote sensing images with label noise,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~59, no.~10, pp. 8798--8811, 2020.

\bibitem{burgert2022multi}
T.~Burgert, M.~Ravanbakhsh, and B.~Demir, ``On the effects of different types of label noise in multi-label remote sensing image classification,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~60, pp. 1--13, 2022.

\bibitem{Aksoy2022multi}
A.~K. Aksoy, M.~Ravanbakhsh, and B.~Demir, ``Multi-label noise robust collaborative learning for remote sensing image classification,'' \emph{IEEE Transactions on Neural Networks and Learning Systems}, pp. 1--14, 2022.

\bibitem{Sumbul2023multilabelgenerative}
G.~Sumbul and B.~Demir, ``Generative reasoning integrated label noise robust deep image representation learning,'' \emph{IEEE Transactions on Image Processing}, pp. 1--1, 2023.

\bibitem{maggiori2016convolutional}
E.~Maggiori, Y.~Tarabalka, G.~Charpiat, and P.~Alliez, ``Convolutional neural networks for large-scale remote-sensing image classification,'' \emph{IEEE Transactions on geoscience and remote sensing}, vol.~55, no.~2, pp. 645--657, 2016.

\bibitem{kaiser2017learning}
P.~Kaiser, J.~D. Wegner, A.~Lucchi, M.~Jaggi, T.~Hofmann, and K.~Schindler, ``Learning aerial image segmentation from online maps,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~55, no.~11, pp. 6054--6068, 2017.

\bibitem{ahmed2021dense}
N.~Ahmed, R.~M. Rahman, M.~S.~G. Adnan, and B.~Ahmed, ``Dense prediction of label noise for learning building extraction from aerial drone imagery,'' \emph{International Journal of Remote Sensing}, vol.~42, no.~23, pp. 8906--8929, 2021.

\bibitem{li2020tranlayer}
P.~Li, X.~He, M.~Qiao, X.~Cheng, Z.~Li, H.~Luo, D.~Song, D.~Li, S.~Hu, R.~Li \emph{et~al.}, ``Robust deep neural networks for road extraction from remote sensing images,'' \emph{IEEE Transactions on Geoscience and Remote Sensing}, vol.~59, no.~7, pp. 6182--6197, 2020.

\bibitem{Zhang202tranlayer}
Z.~Zhang, W.~Guo, M.~Li, and W.~Yu, ``Gis-supervised building extraction with label noise-adaptive fully convolutional neural network,'' \emph{IEEE Geoscience and Remote Sensing Letters}, vol.~17, no.~12, pp. 2135--2139, 2020.

\bibitem{Henry2021boot}
C.~Henry, F.~Fraundorfer, and E.~Vig, ``Aerial road segmentation in the presence of topological label noise,'' in \emph{2020 25th International Conference on Pattern Recognition (ICPR)}, 2021, pp. 2336--2343.

\bibitem{malkin2019superreso}
K.~Malkin, C.~Robinson, L.~Hou, R.~Soobitsky, J.~Czawlytko, D.~Samaras, J.~Saltz, L.~Joppa, and N.~Jojic, ``Label super-resolution networks,'' in \emph{International Conference on Learning Representations}, 2019.

\bibitem{lin2021atten}
C.~Lin, S.~Guo, J.~Chen, L.~Sun, X.~Zheng, Y.~Yang, and Y.~Xiong, ``Deep learning network intensification for preventing noisy-labeled samples for remote sensing classification,'' \emph{Remote Sensing}, vol.~13, no.~9, p. 1689, 2021.

\bibitem{albrecht2020change}
C.~M. Albrecht, R.~Zhang, X.~Cui, M.~Freitag, H.~F. Hamann, L.~J. Klein, U.~Finkler, F.~Marianno, J.~Schmude, N.~Bobroff \emph{et~al.}, ``Change detection from remote sensing to guide openstreetmap labeling,'' \emph{ISPRS International Journal of Geo-Information}, vol.~9, no.~7, p. 427, 2020.

\bibitem{sun2022hsi}
J.~Sun, J.~Liu, L.~Hu, Z.~Wei, and L.~Xiao, ``A mutual teaching framework with momentum correction for unsupervised hyperspectral image change detection,'' \emph{Remote Sensing}, vol.~14, no.~4, 2022.

\bibitem{laine2017selfensemble}
S.~Laine and T.~Aila, ``Temporal ensembling for semi-supervised learning,'' in \emph{International Conference on Learning Representations}, 2017.

\bibitem{arpit2017closer}
D.~Arpit, S.~Jastrzebski, N.~Ballas, D.~Krueger, E.~Bengio, M.~S. Kanwal, T.~Maharaj, A.~Fischer, A.~Courville, Y.~Bengio \emph{et~al.}, ``A closer look at memorization in deep networks,'' in \emph{International conference on machine learning}.\hskip 1em plus 0.5em minus 0.4em\relax PMLR, 2017, pp. 233--242.

\bibitem{dubuisson1994modified}
M.-P. Dubuisson and A.~K. Jain, ``A modified hausdorff distance for object matching,'' in \emph{Proceedings of 12th international conference on pattern recognition}, vol.~1.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, 1994, pp. 566--568.

\bibitem{jadon2020asurvey}
S.~Jadon, ``A survey of loss functions for semantic segmentation,'' in \emph{2020 IEEE Conference on Computational Intelligence in Bioinformatics and Computational Biology (CIBCB)}, 2020, pp. 1--7.

\bibitem{Mnih2013Thesis}
V.~Mnih, ``Machine learning for aerial image labeling,'' Ph.D. dissertation, University of Toronto, 2013.

\bibitem{Skuppin2022igarss}
N.~Skuppin, E.~J. Hoffmann, Y.~Shi, and X.~X. Zhu, ``Building type classification with incomplete labels,'' in \emph{IGARSS 2022 - 2022 IEEE International Geoscience and Remote Sensing Symposium}, 2022, pp. 5844--5847.

\bibitem{ronneberger2015unet}
O.~Ronneberger, P.~Fischer, and T.~Brox, ``U-net: Convolutional networks for biomedical image segmentation,'' in \emph{Medical Image Computing and Computer-Assisted Intervention -- MICCAI 2015}, N.~Navab, J.~Hornegger, W.~M. Wells, and A.~F. Frangi, Eds.\hskip 1em plus 0.5em minus 0.4em\relax Cham: Springer International Publishing, 2015, pp. 234--241.

\bibitem{tan2019efficientnet}
M.~Tan and Q.~Le, ``{E}fficient{N}et: Rethinking model scaling for convolutional neural networks,'' in \emph{Proceedings of the 36th International Conference on Machine Learning}, ser. Proceedings of Machine Learning Research, K.~Chaudhuri and R.~Salakhutdinov, Eds., vol.~97.\hskip 1em plus 0.5em minus 0.4em\relax PMLR, 09--15 Jun 2019, pp. 6105--6114.

\end{thebibliography}
