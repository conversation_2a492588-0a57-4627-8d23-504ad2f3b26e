% DL
@article{zhu2017DLReview,
  title={Deep learning in remote sensing: A comprehensive review and list of resources},
  author={<PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={IEEE geoscience and remote sensing magazine},
  volume={5},
  number={4},
  pages={8--36},
  year={2017},
  publisher={IEEE}
}

@inproceedings{hansch2019truth,
  title={The truth about ground truth: Label noise in human-generated reference data},
  author={H{\"a}n<PERSON>, <PERSON><PERSON> and <PERSON>wich, <PERSON>},
  booktitle={IGARSS 2019-2019 IEEE International Geoscience and Remote Sensing Symposium},
  pages={5594--5597},
  year={2019},
  organization={IEEE}
}


@article{zhu2020so2sat,
  title={So2Sat LCZ42: A benchmark data set for the classification of global local climate zones [Software and Data Sets]},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and others},
  journal={IEEE Geoscience and Remote Sensing Magazine},
  volume={8},
  number={3},
  pages={76--89},
  year={2020},
  publisher={IEEE}
}

@inproceedings{dubuisson1994modified,
  title={A modified Hausdorff distance for object matching},
  author={Dubuisson, M-P and Jain, Anil K},
  booktitle={Proceedings of 12th international conference on pattern recognition},
  volume={1},
  pages={566--568},
  year={1994},
  organization={IEEE}
}


@article{albrecht2020change,
  title={Change detection from remote sensing to guide OpenStreetMap labeling},
  author={Albrecht, Conrad M and Zhang, Rui and Cui, Xiaodong and Freitag, Marcus and Hamann, Hendrik F and Klein, Levente J and Finkler, Ulrich and Marianno, Fernando and Schmude, Johannes and Bobroff, Norman and others},
  journal={ISPRS International Journal of Geo-Information},
  volume={9},
  number={7},
  pages={427},
  year={2020},
  publisher={MDPI}
}


% ssl
@article{Wang2022SSLReview,
  author={Wang, Yi and Albrecht, Conrad M. and Braham, Nassim Ait Ali and Mou, Lichao and Zhu, Xiao Xiang},
  journal={IEEE Geoscience and Remote Sensing Magazine}, 
  title={Self-Supervised Learning in Remote Sensing: A review}, 
  year={2022},
  volume={10},
  number={4},
  pages={213-247},
  doi={10.1109/MGRS.2022.3198244}}
  
% HSI classification review
@ARTICLE{he2018hsireview,
  author={He, Lin and Li, Jun and Liu, Chenying and Li, Shutao},
  journal={IEEE Transactions on Geoscience and Remote Sensing}, 
  title={Recent Advances on Spectral–Spatial Hyperspectral Image Classification: An Overview and New Guidelines}, 
  year={2018},
  volume={56},
  number={3},
  pages={1579-1597},
  doi={10.1109/TGRS.2017.2765364}}

% noisy label sources
% VGI-OSM
@article{Vargas2021OSM,
  author={Vargas-Munoz, John E. and Srivastava, Shivangi and Tuia, Devis and Falcão, Alexandre X.},
  journal={IEEE Geoscience and Remote Sensing Magazine}, 
  title={Open{S}treet{M}ap: Challenges and Opportunities in Machine Learning and Remote Sensing}, 
  year={2021},
  volume={9},
  number={1},
  pages={184-199},
  doi={10.1109/MGRS.2020.2994107}}

@inproceedings{tingzon2023globalsouth,
  title={Towards impactful applications of AI4EO in the Global South},
  author={Isabelle Tingzon and James Matthew Miraflor and Xiao Xiang Zhu and Mrinalini Kochupillai},
  booktitle={Joint Urban Remote Sensing Event (JURSE) 2023},
  year={2023}
}

% autogeolabel
@inproceedings{albrecht2021autogeolabel,
  title={Auto{G}eo{L}abel: Automated Label Generation for Geospatial Machine Learning},
  author={Albrecht, Conrad M and Marianno, Fernando and Klein, Levente J},
  booktitle={2021 IEEE International Conference on Big Data (Big Data)},
  pages={1779--1786},
  year={2021},
  organization={IEEE}
}
% products
% google's dynamic world
@article{brown2022DynamicWorld,
  title={Dynamic World, Near real-time global 10 m land use land cover mapping},
  author={Brown, Christopher F and Brumby, Steven P and Guzder-Williams, Brookie and Birch, Tanya and Hyde, Samantha Brooks and Mazzariello, Joseph and Czerwinski, Wanda and Pasquarella, Valerie J and Haertel, Robert and Ilyushchenko, Simon and others},
  journal={Scientific Data},
  volume={9},
  number={1},
  pages={251},
  year={2022},
  publisher={Nature Publishing Group UK London}
}
% ESA's world cover
@article{Zanaga2021WorldCover,
  author = {Zanaga, Daniele and
            Van De Kerchove, Ruben and
            De Keersmaecker, Wanda and
            Souverijns, Niels and
            Brockmann, Carsten and
            Quast, Ralf and
            Wevers, Jan and
            Grosu, Alex and
            Paccini, Audrey and
            Vergnaud, Sylvain and
            Cartus, Oliver and
            Santoro, Maurizio and
            Fritz, Steffen and
            Georgieva, Ivelina and
            Lesiv, Myroslava and
            Carter, Sarah and
            Herold, Martin and
            Li, Linlin and
            Tsendbazar, Nandin-Erdene and
            Ramoino, Fabrizio and
            Arino, Olivier},
  title        = {ESA WorldCover 10 m 2020 v100},
  month        = oct,
  year         = 2021,
  publisher    = {Zenodo},
  version      = {v100},
  doi          = {10.5281/zenodo.5571936},
  url          = {https://doi.org/10.5281/zenodo.5571936}
}
% ESRI's 
@INPROCEEDINGS{Karra2021ESRI,
  author={Karra, Krishna and Kontgis, Caitlin and Statman-Weil, Zoe and Mazzariello, Joseph C. and Mathis, Mark and Brumby, Steven P.},
  booktitle={2021 IEEE International Geoscience and Remote Sensing Symposium IGARSS}, 
  title={Global land use / land cover with Sentinel 2 and deep learning}, 
  year={2021},
  volume={},
  number={},
  pages={4704-4707},
  doi={10.1109/IGARSS47720.2021.9553499}}
  

% noise label effects
@article{zhang2021understanding,
  title={Understanding deep learning (still) requires rethinking generalization},
  author={Zhang, Chiyuan and Bengio, Samy and Hardt, Moritz and Recht, Benjamin and Vinyals, Oriol},
  journal={Communications of the ACM},
  volume={64},
  number={3},
  pages={107--115},
  year={2021},
  publisher={ACM New York, NY, USA}
}

% memorization effects
@inproceedings{arpit2017closer,
  title={A closer look at memorization in deep networks},
  author={Arpit, Devansh and Jastrzebski, Stanislaw and Ballas, Nicolas and Krueger, David and Bengio, Emmanuel and Kanwal, Maxinder S and Maharaj, Tegan and Fischer, Asja and Courville, Aaron and Bengio, Yoshua and others},
  booktitle={International conference on machine learning},
  pages={233--242},
  year={2017},
  organization={PMLR}
}

% segmentation
@inproceedings{liu2022adele,
  title={Adaptive early-learning correction for segmentation from noisy annotations},
  author={Liu, Sheng and Liu, Kangning and Zhu, Weicheng and Shen, Yiqiu and Fernandez-Granda, Carlos},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={2606--2616},
  year={2022}
}

@inproceedings{liu2022peas,
  author={Liu, Chenying and Albrecht, Conrad M and Wang, Yi and Zhu, Xiao Xiang},
  booktitle={2022 IEEE International Conference on Big Data (Big Data)}, 
  title={Peaks Fusion assisted Early-stopping Strategy for Overhead Imagery Segmentation with Noisy Labels}, 
  year={2022},
  volume={},
  number={},
  pages={4842-4847},
  doi={10.1109/BigData55660.2022.10020164}
}


% ema - self-ensembling
@inproceedings{laine2017selfensemble,
  title={Temporal Ensembling for Semi-Supervised Learning},
  author={Laine, Samuli and Aila, Timo},
  booktitle={International Conference on Learning Representations},
  year={2017},
}

@inproceedings{tarvainen2017meanteacher,
  title={Mean teachers are better role models: Weight-averaged consistency targets improve semi-supervised deep learning results},
  author={Tarvainen, Antti and Valpola, Harri},
  booktitle={Advances in neural information processing systems},
  volume={30},
  year={2017}
}

% datasets
@phdthesis{Mnih2013Thesis,
    author = {Volodymyr Mnih},
    title = {Machine Learning for Aerial Image Labeling},
    school = {University of Toronto},
    year = {2013}
}

@INPROCEEDINGS{Skuppin2022igarss,
  author={Skuppin, Nikolai and Hoffmann, Eike Jens and Shi, Yilei and Zhu, Xiao Xiang},
  booktitle={IGARSS 2022 - 2022 IEEE International Geoscience and Remote Sensing Symposium}, 
  title={Building Type Classification with Incomplete Labels}, 
  year={2022},
  volume={},
  number={},
  pages={5844-5847},
  doi={10.1109/IGARSS46834.2022.9884076}}

% noise sources
@Article{Maiti2022misalign,
AUTHOR = {Maiti, A. and Oude Elberink, S. J. and Vosselman, G.},
TITLE = {EFFECT OF LABEL NOISE IN SEMANTIC SEGMENTATION OF HIGH RESOLUTION AERIAL IMAGES AND HEIGHT DATA},
JOURNAL = {ISPRS Annals of the Photogrammetry, Remote Sensing and Spatial Information Sciences},
VOLUME = {V-2-2022},
YEAR = {2022},
PAGES = {275--282},
DOI = {10.5194/isprs-annals-V-2-2022-275-2022}
}

% models - Unets & efficient nets
@InProceedings{ronneberger2015unet,
author="Ronneberger, Olaf
and Fischer, Philipp
and Brox, Thomas",
editor="Navab, Nassir
and Hornegger, Joachim
and Wells, William M.
and Frangi, Alejandro F.",
title="U-Net: Convolutional Networks for Biomedical Image Segmentation",
booktitle="Medical Image Computing and Computer-Assisted Intervention -- MICCAI 2015",
year="2015",
publisher="Springer International Publishing",
address="Cham",
pages="234--241",
isbn="978-3-319-24574-4"
}

@InProceedings{tan2019efficientnet,
  title = 	 {{E}fficient{N}et: Rethinking Model Scaling for Convolutional Neural Networks},
  author =       {Tan, Mingxing and Le, Quoc},
  booktitle = 	 {Proceedings of the 36th International Conference on Machine Learning},
  pages = 	 {6105--6114},
  year = 	 {2019},
  editor = 	 {Chaudhuri, Kamalika and Salakhutdinov, Ruslan},
  volume = 	 {97},
  series = 	 {Proceedings of Machine Learning Research},
  month = 	 {09--15 Jun},
  publisher =    {PMLR}
}



% pixel-wise correction
@article{cao2022green,
title = {A coarse-to-fine weakly supervised learning method for green plastic cover segmentation using high-resolution remote sensing images},
journal = {ISPRS Journal of Photogrammetry and Remote Sensing},
volume = {188},
pages = {157-176},
year = {2022},
issn = {0924-2716},
author = {Yinxia Cao and Xin Huang},
}
%doi = {https://doi.org/10.1016/j.isprsjprs.2022.04.012},
%url = {https://www.sciencedirect.com/science/article/pii/S0924271622001095},


@article{cao2023building,
  title={A full-level fused cross-task transfer learning method for building change detection using noise-robust pretrained networks on crowdsourced labels},
  author={Cao, Yinxia and Huang, Xin},
  journal={Remote Sensing of Environment},
  volume={284},
  pages={113371},
  year={2023},
  publisher={Elsevier}
}


@ARTICLE{dong2022landcover,
  author={Dong, Runmin and Fang, Weizhen and Fu, Haohuan and Gan, Lin and Wang, Jie and Gong, Peng},
  journal={IEEE Transactions on Geoscience and Remote Sensing}, 
  title={High-Resolution Land Cover Mapping Through Learning With Noise Correction}, 
  year={2022},
  volume={60},
  number={},
  pages={1-13},
  doi={10.1109/TGRS.2021.3068280}}

% regularization
@ARTICLE{wang2020consist,
  author={Wang, Guotai and Liu, Xinglong and Li, Chaoping and Xu, Zhiyong and Ruan, Jiugen and Zhu, Haifeng and Meng, Tao and Li, Kang and Huang, Ning and Zhang, Shaoting},
  journal={IEEE Transactions on Medical Imaging}, 
  title={A Noise-Robust Framework for Automatic Segmentation of COVID-19 Pneumonia Lesions From CT Images}, 
  year={2020},
  volume={39},
  number={8},
  pages={2653-2663},
  doi={10.1109/TMI.2020.3000314}}
  
@INPROCEEDINGS{Henry2021boot,
  author={Henry, Corentin and Fraundorfer, Friedrich and Vig, Eleonora},
  booktitle={2020 25th International Conference on Pattern Recognition (ICPR)}, 
  title={Aerial Road Segmentation in the Presence of Topological Label Noise},
  year={2021},
  volume={},
  number={},
  pages={2336-2343},
  doi={10.1109/ICPR48806.2021.9412054}}

@inproceedings{Reed2015boot,
  author    = {Scott E. Reed and Honglak Lee},
  title     = {Training deep neural networks on noisy labels with bootstrapping},
  year      = {2015},
  booktitle = {International Conference on Learning Representations 2015
(ICLR 2015)},
}

%%%%%%%%%%%%%%%%%%%%%%% related works %%%%%%%%%%%%%%%%%%%%%%% 
% % classification
@inproceedings{wei2022learning,
  title={Learning with Noisy Labels Revisited: A Study Using Real-World Human Annotations},
  author={Wei, Jiaheng and Zhu, Zhaowei and Cheng, Hao and Liu, Tongliang and Niu, Gang and Liu, Yang},
  booktitle={International Conference on Learning Representations},
  year={2022}
}

@article{song2022learning,
  title={Learning from noisy labels with deep neural networks: A survey},
  author={Song, Hwanjun and Kim, Minseok and Park, Dongmin and Shin, Yooju and Lee, Jae-Gil},
  journal={IEEE Transactions on Neural Networks and Learning Systems},
  year={2022},
  publisher={IEEE}
}

% robust architecture
@inproceedings{bekker2016training,
  title={Training deep neural-networks based on unreliable labels},
  author={Bekker, Alan Joseph and Goldberger, Jacob},
  booktitle={2016 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)},
  pages={2682--2686},
  year={2016},
  organization={IEEE}
}

@article{sukhbaatar2014training,
  title={Training convolutional networks with noisy labels},
  author={Sukhbaatar, Sainbayar and Bruna, Joan and Paluri, Manohar and Bourdev, Lubomir and Fergus, Rob},
  journal={arXiv preprint arXiv:1406.2080},
  year={2014}
}

@inproceedings{goldberger2017training,
  title={Training deep neural-networks using a noise adaptation layer},
  author={Goldberger, Jacob and Ben-Reuven, Ehud},
  booktitle={International conference on learning representations},
  year={2017}
}

% sample selection/correction
@inproceedings{tanaka2018joint,
  title={Joint optimization framework for learning with noisy labels},
  author={Tanaka, Daiki and Ikami, Daiki and Yamasaki, Toshihiko and Aizawa, Kiyoharu},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={5552--5560},
  year={2018}
}

@article{pleiss2020identifying,
  title={Identifying mislabeled data using the area under the margin ranking},
  author={Pleiss, Geoff and Zhang, Tianyi and Elenberg, Ethan and Weinberger, Kilian Q},
  journal={Advances in Neural Information Processing Systems},
  volume={33},
  pages={17044--17056},
  year={2020}
}

@article{malach2017decoupling,
  title={Decoupling" when to update" from" how to update"},
  author={Malach, Eran and Shalev-Shwartz, Shai},
  journal={Advances in neural information processing systems},
  volume={30},
  year={2017}
}

@inproceedings{jiang2018mentornet,
  title={MentorNet: Learning Data-Driven Curriculum for Very Deep Neural Networks on Corrupted Labels},
  author={Jiang, Lu and Zhou, Zhengyuan and Leung, Thomas and Li, Li-Jia and Fei-Fei, Li},
  booktitle={ICML},
  year={2018}
}

@article{han2018coteaching,
  title={Co-teaching: Robust training of deep neural networks with extremely noisy labels},
  author={Han, Bo and Yao, Quanming and Yu, Xingrui and Niu, Gang and Xu, Miao and Hu, Weihua and Tsang, Ivor and Sugiyama, Masashi},
  journal={Advances in neural information processing systems},
  volume={31},
  year={2018}
}

@inproceedings{yu2019coteachingplus,
  title={How does disagreement help generalization against label corruption?},
  author={Yu, Xingrui and Han, Bo and Yao, Jiangchao and Niu, Gang and Tsang, Ivor and Sugiyama, Masashi},
  booktitle={International Conference on Machine Learning},
  pages={7164--7173},
  year={2019},
  organization={PMLR}
}

@inproceedings{zhang2022ideal,
  title={IDEAL: High-order-ensemble adaptation network for learning with noisy labels},
  author={Zhang, Peng-Fei and Huang, Zi and Bai, Guangdong and Xu, Xin-Shun},
  booktitle={Proceedings of the 30th ACM International Conference on Multimedia},
  pages={325--333},
  year={2022}
}

@inproceedings{li2020dividemix,
  title={DivideMix: Learning with Noisy Labels as Semi-supervised Learning},
  author={Li, Junnan and Socher, Richard and Hoi, Steven CH},
  booktitle={International Conference on Learning Representations},
  year={2020}
}

% loss
@article{manwani2013noise,
  title={Noise tolerance under risk minimization},
  author={Manwani, Naresh and Sastry, PS},
  journal={IEEE transactions on cybernetics},
  volume={43},
  number={3},
  pages={1146--1151},
  year={2013},
  publisher={IEEE}
}

@inproceedings{ghosh2017robust,
  title={Robust loss functions under label noise for deep neural networks},
  author={Ghosh, Aritra and Kumar, Himanshu and Sastry, P Shanti},
  booktitle={Proceedings of the AAAI conference on artificial intelligence},
  volume={31},
  number={1},
  year={2017}
}

@article{liu2020elr,
  title={Early-learning regularization prevents memorization of noisy labels},
  author={Liu, Sheng and Niles-Weed, Jonathan and Razavian, Narges and Fernandez-Granda, Carlos},
  journal={Advances in neural information processing systems},
  volume={33},
  pages={20331--20342},
  year={2020}
}

@inproceedings{yi2019pencil,
  title={Probabilistic end-to-end noise correction for learning with noisy labels},
  author={Yi, Kun and Wu, Jianxin},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={7017--7025},
  year={2019}
}

@article{zhang2018generalized,
  title={Generalized cross entropy loss for training deep neural networks with noisy labels},
  author={Zhang, Zhilu and Sabuncu, Mert},
  journal={Advances in neural information processing systems},
  volume={31},
  year={2018}
}

@article{lyu2019curriculum,
  title={Curriculum loss: Robust learning and generalization against label corruption},
  author={Lyu, Yueming and Tsang, Ivor W},
  journal={arXiv preprint arXiv:1905.10045},
  year={2019}
}

@inproceedings{liu2020peer,
  title={Peer loss functions: Learning from noisy labels without knowing noise rates},
  author={Liu, Yang and Guo, Hongyi},
  booktitle={International conference on machine learning},
  pages={6226--6236},
  year={2020},
  organization={PMLR}
}

% classification in RS
@article{tai2020coastal,
  title={Coastal image classification under noisy labels},
  author={Tai, Xiaoxiao and Wang, Guangxing and Grecos, Christos and Ren, Peng},
  journal={Journal of Coastal Research},
  volume={102},
  number={SI},
  pages={151--156},
  year={2020},
  publisher={Coastal Education and Research Foundation}
}

@article{huang2020smoothloss,
  title={Classification of large-scale high-resolution SAR images with deep transfer learning},
  author={Huang, Zhongling and Dumitru, Corneliu Octavian and Pan, Zongxu and Lei, Bin and Datcu, Mihai},
  journal={IEEE Geoscience and Remote Sensing Letters},
  volume={18},
  number={1},
  pages={107--111},
  year={2020},
  publisher={IEEE}
}

@article{damodaran2020entropic,
  title={An entropic optimal transport loss for learning deep neural networks under label noise in remote sensing images},
  author={Damodaran, Bharath Bhushan and Flamary, R{\'e}mi and Seguy, Vivien and Courty, Nicolas},
  journal={Computer Vision and Image Understanding},
  volume={191},
  pages={102863},
  year={2020},
  publisher={Elsevier}
}

@article{kang2021neighbor,
  title={Noise-tolerant deep neighborhood embedding for remotely sensed images with label noise},
  author={Kang, Jian and Fernandez-Beltran, Ruben and Kang, Xudong and Ni, Jingen and Plaza, Antonio},
  journal={IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing},
  volume={14},
  pages={2551--2562},
  year={2021},
  publisher={IEEE}
}

@article{kang2020rnsl,
  title={Robust normalized softmax loss for deep metric learning-based characterization of remote sensing images with label noise},
  author={Kang, Jian and Fernandez-Beltran, Ruben and Duan, Puhong and Kang, Xudong and Plaza, Antonio J},
  journal={IEEE Transactions on Geoscience and Remote Sensing},
  volume={59},
  number={10},
  pages={8798--8811},
  year={2020},
  publisher={IEEE}
}

@article{li2020error,
  title={Error-tolerant deep learning for remote sensing image scene classification},
  author={Li, Yansheng and Zhang, Yongjun and Zhu, Zhihui},
  journal={IEEE transactions on cybernetics},
  volume={51},
  number={4},
  pages={1756--1768},
  year={2020},
  publisher={IEEE}
}

@article{tu2020robust,
  title={Robust learning of mislabeled training samples for remote sensing image scene classification},
  author={Tu, Bing and Kuang, Wenlan and He, Wangquan and Zhang, Guoyun and Peng, Yishu},
  journal={IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing},
  volume={13},
  pages={5623--5639},
  year={2020},
  publisher={IEEE}
}

@article{burgert2022multi,
  title={On the effects of different types of label noise in multi-label remote sensing image classification},
  author={Burgert, Tom and Ravanbakhsh, Mahdyar and Demir, Beg{\"u}m},
  journal={IEEE Transactions on Geoscience and Remote Sensing},
  volume={60},
  pages={1--13},
  year={2022},
  publisher={IEEE}
}

@ARTICLE{Aksoy2022multi,
  author={Aksoy, Ahmet Kerem and Ravanbakhsh, Mahdyar and Demir, Begüm},
  journal={IEEE Transactions on Neural Networks and Learning Systems}, 
  title={Multi-Label Noise Robust Collaborative Learning for Remote Sensing Image Classification}, 
  year={2022},
  volume={},
  number={},
  pages={1-14},
  doi={10.1109/TNNLS.2022.3209992}}
  
% % segmentation
% weak supervision
@article{shen2023survey,
  title={A survey on label-efficient deep image segmentation: Bridging the gap between weak supervision and dense prediction},
  author={Shen, Wei and Peng, Zelin and Wang, Xuehui and Wang, Huayu and Cen, Jiazhong and Jiang, Dongsheng and Xie, Lingxi and Yang, Xiaokang and Tian, Q},
  journal={IEEE Transactions on Pattern Analysis and Machine Intelligence},
  year={2023},
  publisher={IEEE}
}

@inproceedings{zhang2020reliability,
  title={Reliability does matter: An end-to-end weakly supervised semantic segmentation approach},
  author={Zhang, Bingfeng and Xiao, Jimin and Wei, Yunchao and Sun, Mingjie and Huang, Kaizhu},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={34},
  number={07},
  pages={12765--12772},
  year={2020}
}

% with clean labels
@article{maggiori2016convolutional,
  title={Convolutional neural networks for large-scale remote-sensing image classification},
  author={Maggiori, Emmanuel and Tarabalka, Yuliya and Charpiat, Guillaume and Alliez, Pierre},
  journal={IEEE Transactions on geoscience and remote sensing},
  volume={55},
  number={2},
  pages={645--657},
  year={2016},
  publisher={IEEE}
}

@article{kaiser2017learning,
  title={Learning aerial image segmentation from online maps},
  author={Kaiser, Pascal and Wegner, Jan Dirk and Lucchi, Aur{\'e}lien and Jaggi, Martin and Hofmann, Thomas and Schindler, Konrad},
  journal={IEEE Transactions on Geoscience and Remote Sensing},
  volume={55},
  number={11},
  pages={6054--6068},
  year={2017},
  publisher={IEEE}
}

@article{ahmed2021dense,
  title={Dense prediction of label noise for learning building extraction from aerial drone imagery},
  author={Ahmed, Nahian and Rahman, Rashedur M and Adnan, Mohammed Sarfaraz Gani and Ahmed, Bayes},
  journal={International Journal of Remote Sensing},
  volume={42},
  number={23},
  pages={8906--8929},
  year={2021},
  publisher={Taylor \& Francis}
}

% RS- architecture
@article{li2020tranlayer,
  title={Robust deep neural networks for road extraction from remote sensing images},
  author={Li, Panle and He, Xiaohui and Qiao, Mengjia and Cheng, Xijie and Li, Zhiqiang and Luo, Haotian and Song, Dingjun and Li, Daidong and Hu, Shaokai and Li, Runchuan and others},
  journal={IEEE Transactions on Geoscience and Remote Sensing},
  volume={59},
  number={7},
  pages={6182--6197},
  year={2020},
  publisher={IEEE}
}

@ARTICLE{Zhang202tranlayer,
  author={Zhang, Zenghui and Guo, Weiwei and Li, Mingjie and Yu, Wenxian},
  journal={IEEE Geoscience and Remote Sensing Letters}, 
  title={GIS-Supervised Building Extraction With Label Noise-Adaptive Fully Convolutional Neural Network}, 
  year={2020},
  volume={17},
  number={12},
  pages={2135-2139},
  doi={10.1109/LGRS.2019.2963065}}
  
% loss
@inproceedings{malkin2019superreso,
  title={Label super-resolution networks},
  author={Malkin, Kolya and Robinson, Caleb and Hou, Le and Soobitsky, Rachel and Czawlytko, Jacob and Samaras, Dimitris and Saltz, Joel and Joppa, Lucas and Jojic, Nebojsa},
  booktitle={International Conference on Learning Representations},
  year={2019}
}

@article{lin2021atten,
  title={Deep learning network intensification for preventing noisy-labeled samples for remote sensing classification},
  author={Lin, Chuang and Guo, Shanxin and Chen, Jinsong and Sun, Luyi and Zheng, Xiaorou and Yang, Yan and Xiong, Yingfei},
  journal={Remote Sensing},
  volume={13},
  number={9},
  pages={1689},
  year={2021},
  publisher={MDPI}
}

@ARTICLE{li2022road,
  author={Li, Panle and He, Xiaohui and Qiao, Mengjia and Cheng, Xijie and Li, Jiamian and Guo, Xiaoyu and Zhou, Tao and Song, Dingjun and Chen, Mingyang and Miao, Disheng and Jiang, Yinjie and Tian, Zhihui},
  journal={IEEE Transactions on Geoscience and Remote Sensing}, 
  title={Exploring Label Probability Sequence to Robustly Learn Deep Convolutional Neural Networks for Road Extraction With Noisy Datasets}, 
  year={2022},
  volume={60},
  number={},
  pages={1-18},
  doi={10.1109/TGRS.2021.3128539}}


@ARTICLE{Sumbul2023multilabelgenerative,
  author={Sumbul, Gencer and Demir, Begüm},
  journal={IEEE Transactions on Image Processing}, 
  title={Generative Reasoning Integrated Label Noise Robust Deep Image Representation Learning}, 
  year={2023},
  volume={},
  number={},
  pages={1-1},
  doi={10.1109/TIP.2023.3293776}}
  
  
@ARTICLE{zhang2023selftrain,
  author={Zhang, Fahong and Shi, Yilei and Xiong, Zhitong and Huang, Wei and Zhu, Xiao Xiang},
  journal={IEEE Transactions on Geoscience and Remote Sensing}, 
  title={Pseudo Features-Guided Self-Training for Domain Adaptive Semantic Segmentation of Satellite Images}, 
  year={2023},
  volume={61},
  number={},
  pages={1-14},
  doi={10.1109/TGRS.2023.3281503}}

@article{gawlikowski2023uncertianty,
  title={A survey of uncertainty in deep neural networks},
  author={Gawlikowski, Jakob and Tassi, Cedrique Rovile Njieutcheu and Ali, Mohsin and Lee, Jongseok and Humt, Matthias and Feng, Jianxiang and Kruspe, Anna and Triebel, Rudolph and Jung, Peter and Roscher, Ribana and others},
  journal={Artificial Intelligence Review},
  pages={1--77},
  year={2023},
  publisher={Springer}
}

%% OSM quality assessment
@article{brovelli2018osmbuildingassess,
  title={A new method for the assessment of spatial accuracy and completeness of OpenStreetMap building footprints},
  author={Brovelli, Maria Antonia and Zamboni, Giorgio},
  journal={ISPRS International Journal of Geo-Information},
  volume={7},
  number={8},
  pages={289},
  year={2018},
  publisher={MDPI}
}

@article{zhang2022osmbuildingassess,
author = {Yuheng Zhang and Qi Zhou and Maria Antonia Brovelli and Wanjing Li},
title = {Assessing OSM building completeness using population data},
journal = {International Journal of Geographical Information Science},
volume = {36},
number = {7},
pages = {1443-1466},
year  = {2022},
publisher = {Taylor & Francis},
doi = {10.1080/13658816.2021.2023158},
}

@article{herfort2023buildingcomplete,
  title={A spatio-temporal analysis investigating completeness and inequalities of global urban building data in OpenStreetMap},
  author={Herfort, Benjamin and Lautenbach, Sven and Porto de Albuquerque, Jo{\~a}o and Anderson, Jennings and Zipf, Alexander},
  journal={Nature Communications},
  volume={14},
  number={1},
  pages={3985},
  year={2023},
  publisher={Nature Publishing Group UK London}
}

@INPROCEEDINGS{jadon2020asurvey,
  author={Jadon, Shruti},
  booktitle={2020 IEEE Conference on Computational Intelligence in Bioinformatics and Computational Biology (CIBCB)}, 
  title={A survey of loss functions for semantic segmentation}, 
  year={2020},
  volume={},
  number={},
  pages={1-7},
  doi={10.1109/CIBCB48159.2020.9277638}}


@Article{sun2022hsi,
AUTHOR = {Sun, Jia and Liu, Jia and Hu, Ling and Wei, Zhihui and Xiao, Liang},
TITLE = {A Mutual Teaching Framework with Momentum Correction for Unsupervised Hyperspectral Image Change Detection},
JOURNAL = {Remote Sensing},
VOLUME = {14},
YEAR = {2022},
NUMBER = {4},
ARTICLE-NUMBER = {1000},
ISSN = {2072-4292},
DOI = {10.3390/rs14041000}
}
%URL = {https://www.mdpi.com/2072-4292/14/4/1000},


